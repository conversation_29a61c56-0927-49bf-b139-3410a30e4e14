import pandas as pd

a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\SOLD_UNITS_vs_DELIVERED_UNITS\24w28_25w27.parquet")

a.head()


import numpy as np

# Assuming your dataframe is called 'a'
# Create the division column using np.select for multiple conditions

conditions = [
    a['dep'].isin(['BWS', 'HEA', 'DRY']),  # GROCERY condition
    a['dep'] == 'HDL',                     # GM condition
    a['dep'].isin(['DAI', 'PPD', 'FRZ', 'SFM', 'SFP']),  # FRESH condition
    a['dep'] == 'PRO',                  # PRODUCE condition
    a['dep'].isin(['CHC', 'DEC', 'FIC', 'MPC', 'HFG']),
    a['dep'].isin(['ISB', 'SFB'])
]

choices = [
    'GROCERY',
    'GM', 
    'FRESH',
    'PRODUCE',
    'COUNTERS',
    'BAKERY'
]

# For all other cases, use the 'dep name' column value
# The default parameter handles cases where none of the conditions are met
a['division'] = np.select(conditions, choices, default=a['dep name'])

a.head()

a.division.unique()

a[(a.pmg == "DEC02")&(a.store == 11002)].head()

b = a[['dep','pmg', 'dep name', 'pmg_name', 'pmg_name_total', 'division']].drop_duplicates().sort_values(by="pmg").query("pmg_name_total.notna()")

c = a.drop(['dep name', 'pmg_name', 'pmg_name_total', 'division'], axis=1).merge(b, on=['dep', 'pmg'], how='left')

c.shape

a.shape

import xlwings as xw

xw.view(b,table=False)

a.shape

a[['store', 'week', 'pmg']].drop_duplicates().shape

a.to_parquet(r"C:\Users\<USER>\OneDrive - Tesco\#MODELS\##WebApps\productivity_dashboard\24w28_25w27.parquet")

import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from scipy import stats





a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\SOLD_UNITS_vs_DELIVERED_UNITS\24w28_25w27.parquet")


df_country = a.groupby(['country','week','dep name'], as_index=False)[["sold_units", "unit_delivered"]].sum()

df_total = df_country.groupby(["country", "week"])[['sold_units', 'unit_delivered']].sum().reset_index()






def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                            separator=None, auto_open=False):
    with open(html_fname, 'w') as f:
        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))
        for fig in plotly_figs[1:]:
            if separator:
                f.write(separator)
            f.write(fig.to_html(full_html=False, include_plotlyjs=False))

    if auto_open:
        import pathlib, webbrowser
        uri = pathlib.Path(html_fname).absolute().as_uri()
        webbrowser.open(uri)

def parse_custom_week(week_str):
    year = int(week_str[1:5])
    week = int(week_str[6:8])
    return year * 100 + week

def format_week_label(week_str):
    return f"w{week_str[6:8]}"

def add_trendline(x, y):
    x_numeric = x.apply(parse_custom_week)
    slope, intercept, r_value, p_value, std_err = stats.linregress(x_numeric, y)
    line = slope * x_numeric + intercept
    return x, line

def format_large_number(num):
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return f"{num:.0f}"

# Cool color palette
colors = {'CZ': ['#4e79a7', '#a0cbe8'], 'HU': ['#59a14f', '#8cd17d'], 'SK': ['#9c755f', '#c9b18f']}
cont = []

# Create the Total chart with dark theme
fig_total = make_subplots(rows=len(df_total['country'].unique()), cols=1, 
                          shared_xaxes=True,
                          vertical_spacing=0.02,
                          subplot_titles=["" for _ in df_total['country'].unique()])

for i, country in enumerate(df_total['country'].unique(), start=1):
    df_country_filtered = df_total[df_total['country'] == country]
    
    week_labels = df_country_filtered["week"].apply(format_week_label)
    
    fig_total.add_trace(
        go.Bar(x=week_labels, y=df_country_filtered["sold_units"],
            name="Sold Units", marker_color=colors[country][0],
            showlegend=(i==1), opacity=0.8,
            hovertemplate="<b>Sold Units</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
            customdata=[format_large_number(x) for x in df_country_filtered["sold_units"]],
            offset=-0.2, width=0.4),
        row=i, col=1
    )
    
    fig_total.add_trace(
        go.Bar(x=week_labels, y=df_country_filtered["unit_delivered"],
            name="Units Delivered", marker_color=colors[country][1],
            showlegend=(i==1), opacity=0.8,
            hovertemplate="<b>Units Delivered</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
            customdata=[format_large_number(x) for x in df_country_filtered["unit_delivered"]],
            offset=0.2, width=0.4),
        row=i, col=1
    )
    
    for column, color, name in [("sold_units", "#FFA500", "Sold Units Trend"), 
                                ("unit_delivered", "#FF4136", "Units Delivered Trend")]:
        x_trend, y_trend = add_trendline(df_country_filtered["week"], df_country_filtered[column])
        fig_total.add_trace(
            go.Scatter(x=x_trend.apply(format_week_label), y=y_trend, mode='lines', name=name,
                       line=dict(color=color, width=1.5, dash='solid'), showlegend=(i==1),
                       hovertemplate=f"<b>{name}</b><br>Week: %{{x}}<br>Units: %{{customdata}}<extra></extra>",
                       customdata=[format_large_number(x) for x in y_trend]),
            row=i, col=1
        )

fig_total.update_layout(
    title_text="<b>Sales and Deliveries - Total</b>",
    title_font=dict(size=24, color='white'),
    barmode='overlay',
    bargap=0,
    bargroupgap=0,
    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1, font=dict(color='white')),
    showlegend=True,
    margin=dict(l=100, r=100, t=100, b=50),
    plot_bgcolor='#1E1E1E',
    paper_bgcolor='#121212',
    font=dict(family="Arial, sans-serif", size=12, color="white"),
    hoverlabel=dict(bgcolor="white", font_size=12, font_color="black")
)

fig_total.update_xaxes(
    title_text="",
    tickangle=45,
    gridcolor='#333333',
    tickfont=dict(size=12, color='white'),
    title_font=dict(size=12, color='white')
)

fig_total.update_yaxes(
    title_text="",
    gridcolor='#333333',
    tickformat=',d',
    title_font=dict(size=10, color='white'),
    tickfont=dict(size=12, color='white')
)

for i in range(1, len(df_total['country'].unique())):
    fig_total.update_xaxes(title_text="", row=i, col=1)

for i, country in enumerate(df_total['country'].unique(), start=1):
    fig_total.update_xaxes(showline=True, linewidth=1, linecolor='#444444', mirror=True, row=i, col=1)
    fig_total.update_yaxes(showline=True, linewidth=1, linecolor='#444444', mirror=True, row=i, col=1)
    
    fig_total.add_annotation(
        text=f"<b>{country}</b>",
        xref="paper", yref="paper",
        x=1.02, y=1 - (i-1)/len(df_total['country'].unique()) - 0.5/len(df_total['country'].unique()),
        showarrow=False,
        font=dict(size=14, color='white'),
        xanchor="left", yanchor="middle"
    )

cont.append(fig_total)

# Original script for individual departments
for x in df_country['dep name'].unique().tolist():

    # Filter the dataframe for the current department
    df_filtered = df_country[df_country['dep name'] == x].copy()
    
    # Get unique countries for this department
    countries = df_filtered['country'].unique()

    fig = make_subplots(rows=len(countries), cols=1, 
                        shared_xaxes=True,
                        vertical_spacing=0.02,
                        subplot_titles=["" for _ in countries])

    for i, country in enumerate(countries, start=1):
        df_country_filtered = df_filtered[df_filtered['country'] == country]
        
        week_labels = df_country_filtered["week"].apply(format_week_label)
        
        fig.add_trace(
            go.Bar(x=week_labels, y=df_country_filtered["sold_units"],
                name="Sold Units", marker_color=colors[country][0],
                showlegend=(i==1), opacity=0.8,
                hovertemplate="<b>Sold Units</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
                customdata=[format_large_number(x) for x in df_country_filtered["sold_units"]],
                offset=-0.2, width=0.4),
            row=i, col=1
        )
        
        fig.add_trace(
            go.Bar(x=week_labels, y=df_country_filtered["unit_delivered"],
                name="Units Delivered", marker_color=colors[country][1],
                showlegend=(i==1), opacity=0.8,
                hovertemplate="<b>Units Delivered</b><br>Week: %{x}<br>Units: %{customdata}<extra></extra>",
                customdata=[format_large_number(x) for x in df_country_filtered["unit_delivered"]],
                offset=0.2, width=0.4),
            row=i, col=1
        )
        
        for column, color, name in [("sold_units", "orange", "Sold Units Trend"), 
                                    ("unit_delivered", "red", "Units Delivered Trend")]:
            x_trend, y_trend = add_trendline(df_country_filtered["week"], df_country_filtered[column])
            fig.add_trace(
                go.Scatter(x=x_trend.apply(format_week_label), y=y_trend, mode='lines', name=name,
                           line=dict(color=color, width=1.5, dash='solid'), showlegend=(i==1),
                           hovertemplate=f"<b>{name}</b><br>Week: %{{x}}<br>Units: %{{customdata}}<extra></extra>",
                           customdata=[format_large_number(x) for x in y_trend]),
                row=i, col=1
            )

    fig.update_layout(
        title_text=f"<b>Sales and Deliveries - {x}</b>",
        title_font=dict(size=24),
        barmode='overlay',
        bargap=0,
        bargroupgap=0,
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        showlegend=True,
        margin=dict(l=100, r=100, t=100, b=50),
        plot_bgcolor='white',
        paper_bgcolor='white',
        font=dict(family="Arial, sans-serif", size=12, color="#333333"),
        hoverlabel=dict(bgcolor="black", font_size=12, font_color="white")
    )

    fig.update_xaxes(
        title_text="",
        tickangle=45,
        gridcolor='#f0f0f0',
        tickfont=dict(size=12),
        title_font=dict(size=12)
    )

    fig.update_yaxes(
        title_text="",
        gridcolor='#f0f0f0',
        tickformat=',d',
        title_font=dict(size=10),
        tickfont=dict(size=12)
    )

    for i in range(1, len(countries)):
        fig.update_xaxes(title_text="", row=i, col=1)

    for i, country in enumerate(countries, start=1):
        fig.update_xaxes(showline=True, linewidth=1, linecolor='#e0e0e0', mirror=True, row=i, col=1)
        fig.update_yaxes(showline=True, linewidth=1, linecolor='#e0e0e0', mirror=True, row=i, col=1)
        
        fig.add_annotation(
            text=f"<b>{country}</b>",
            xref="paper", yref="paper",
            x=1.02, y=1 - (i-1)/len(countries) - 0.5/len(countries),
            showarrow=False,
            font=dict(size=14),
            xanchor="left", yanchor="middle"
        )

    cont.append(fig)

# Generate the HTML file
html_fname = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\SOLD_UNITS_vs_DELIVERED_UNITS\Sales_and_Delivers_24w26-25w26.html"
plotly_figs = cont
combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                        separator=None, auto_open=False)